# -*- coding: utf-8 -*-
"""
序列模拟器 - 键盘按键序列模拟程序
支持格式：[a,1]-[b,2]-[c,1] 表示按键a等待1秒，按键b等待2秒，按键c等待1秒
使用 ~ 键开始/停止程序
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import re
import sys
from typing import List, Tuple, Optional

try:
    from pynput import keyboard
    from pynput.keyboard import Key, Listener
except ImportError:
    print("请安装pynput库: pip install pynput")
    sys.exit(1)


class SequenceSimulator:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("序列模拟器")
        self.root.geometry("600x500")
        self.root.resizable(True, True)
        
        # 状态变量
        self.is_running = False
        self.current_thread = None
        self.keyboard_listener = None
        self.should_stop = False
        
        # 序列数据
        self.sequence = []
        self.loop_mode = "once"  # once, multiple, infinite
        self.loop_count = 1
        
        self.setup_ui()
        self.start_keyboard_listener()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="序列模拟器", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 20))
        
        # 序列输入
        ttk.Label(main_frame, text="序列格式:").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.sequence_entry = ttk.Entry(main_frame, width=50)
        self.sequence_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=5)
        self.sequence_entry.insert(0, "[a,1]-[b,2]-[c,1]")
        
        # 格式说明
        format_label = ttk.Label(main_frame, text="格式: [按键,延迟秒数]-[按键,延迟秒数]...", 
                                font=("Arial", 9), foreground="gray")
        format_label.grid(row=2, column=1, sticky=tk.W, pady=(0, 10))
        
        # 循环设置框架
        loop_frame = ttk.LabelFrame(main_frame, text="循环设置", padding="10")
        loop_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        loop_frame.columnconfigure(1, weight=1)
        
        # 循环模式
        self.loop_var = tk.StringVar(value="once")
        ttk.Radiobutton(loop_frame, text="执行1次", variable=self.loop_var, 
                       value="once").grid(row=0, column=0, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(loop_frame, text="执行多次", variable=self.loop_var, 
                       value="multiple").grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(loop_frame, text="无限循环", variable=self.loop_var, 
                       value="infinite").grid(row=0, column=2, sticky=tk.W)
        
        # 循环次数设置
        count_frame = ttk.Frame(loop_frame)
        count_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        ttk.Label(count_frame, text="循环次数:").grid(row=0, column=0, sticky=tk.W)
        self.count_var = tk.StringVar(value="1")
        self.count_spinbox = ttk.Spinbox(count_frame, from_=1, to=999, width=10, 
                                        textvariable=self.count_var)
        self.count_spinbox.grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=2, pady=20)
        
        self.start_button = ttk.Button(control_frame, text="开始 (或按 ~ 键)", 
                                      command=self.start_sequence)
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止 (或按 ~ 键)", 
                                     command=self.stop_sequence, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Button(control_frame, text="解析序列", 
                  command=self.parse_sequence).grid(row=0, column=2)
        
        # 状态显示
        self.status_var = tk.StringVar(value="就绪 - 按 ~ 键开始/停止")
        status_label = ttk.Label(main_frame, textvariable=self.status_var, 
                                font=("Arial", 10), foreground="blue")
        status_label.grid(row=5, column=0, columnspan=2, pady=10)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="执行日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=70)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def log_message(self, message: str):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def parse_sequence(self) -> bool:
        """解析序列字符串"""
        sequence_str = self.sequence_entry.get().strip()
        if not sequence_str:
            messagebox.showerror("错误", "请输入序列")
            return False
            
        # 正则表达式匹配 [按键,延迟] 格式
        pattern = r'\[([^,]+),(\d+(?:\.\d+)?)\]'
        matches = re.findall(pattern, sequence_str)
        
        if not matches:
            messagebox.showerror("错误", "序列格式错误\n正确格式: [a,1]-[b,2]-[c,1]")
            return False
            
        self.sequence = []
        for key, delay in matches:
            try:
                delay_float = float(delay)
                self.sequence.append((key.strip(), delay_float))
            except ValueError:
                messagebox.showerror("错误", f"延迟时间格式错误: {delay}")
                return False
                
        self.log_message(f"解析成功: {len(self.sequence)} 个按键序列")
        for i, (key, delay) in enumerate(self.sequence):
            self.log_message(f"  {i+1}. 按键 '{key}' 延迟 {delay} 秒")
            
        return True

    def start_keyboard_listener(self):
        """启动键盘监听器"""
        def on_press(key):
            try:
                if hasattr(key, 'char') and key.char == '~':
                    if self.is_running:
                        self.stop_sequence()
                    else:
                        self.start_sequence()
            except AttributeError:
                pass

        self.keyboard_listener = Listener(on_press=on_press)
        self.keyboard_listener.start()

    def start_sequence(self):
        """开始执行序列"""
        if self.is_running:
            return

        if not self.parse_sequence():
            return

        self.is_running = True
        self.should_stop = False
        self.start_button.config(state="disabled")
        self.stop_button.config(state="normal")

        # 获取循环设置
        self.loop_mode = self.loop_var.get()
        try:
            self.loop_count = int(self.count_var.get())
        except ValueError:
            self.loop_count = 1

        self.status_var.set("正在执行序列...")
        self.log_message("开始执行序列")

        # 在新线程中执行序列
        self.current_thread = threading.Thread(target=self.execute_sequence)
        self.current_thread.daemon = True
        self.current_thread.start()

    def stop_sequence(self):
        """停止执行序列"""
        if not self.is_running:
            return

        self.should_stop = True
        self.is_running = False
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.status_var.set("已停止")
        self.log_message("序列执行已停止")

    def execute_sequence(self):
        """执行按键序列"""
        try:
            if self.loop_mode == "once":
                self.run_sequence_once()
            elif self.loop_mode == "multiple":
                for i in range(self.loop_count):
                    if self.should_stop:
                        break
                    self.log_message(f"执行第 {i+1}/{self.loop_count} 次循环")
                    self.run_sequence_once()
            elif self.loop_mode == "infinite":
                loop_count = 0
                while not self.should_stop:
                    loop_count += 1
                    self.log_message(f"执行第 {loop_count} 次循环")
                    self.run_sequence_once()

        except Exception as e:
            self.log_message(f"执行出错: {str(e)}")
        finally:
            self.is_running = False
            self.root.after(0, self.reset_ui)

    def run_sequence_once(self):
        """执行一次完整序列"""
        for i, (key, delay) in enumerate(self.sequence):
            if self.should_stop:
                break

            # 模拟按键
            self.simulate_key(key)
            self.log_message(f"按键 '{key}' 已执行")

            # 延迟
            if delay > 0 and not self.should_stop:
                self.log_message(f"等待 {delay} 秒...")
                time.sleep(delay)

    def simulate_key(self, key_str: str):
        """模拟按键"""
        try:
            # 创建键盘控制器
            controller = keyboard.Controller()

            # 处理特殊按键
            special_keys = {
                'space': Key.space,
                'enter': Key.enter,
                'tab': Key.tab,
                'shift': Key.shift,
                'ctrl': Key.ctrl,
                'alt': Key.alt,
                'esc': Key.esc,
                'backspace': Key.backspace,
                'delete': Key.delete,
                'up': Key.up,
                'down': Key.down,
                'left': Key.left,
                'right': Key.right,
                'home': Key.home,
                'end': Key.end,
                'page_up': Key.page_up,
                'page_down': Key.page_down,
            }

            key_lower = key_str.lower()
            if key_lower in special_keys:
                controller.press(special_keys[key_lower])
                controller.release(special_keys[key_lower])
            else:
                # 普通字符按键
                controller.press(key_str)
                controller.release(key_str)

        except Exception as e:
            self.log_message(f"按键模拟失败 '{key_str}': {str(e)}")

    def reset_ui(self):
        """重置UI状态"""
        self.start_button.config(state="normal")
        self.stop_button.config(state="disabled")
        self.status_var.set("就绪 - 按 ~ 键开始/停止")

    def on_closing(self):
        """程序关闭时的清理工作"""
        self.should_stop = True
        if self.keyboard_listener:
            self.keyboard_listener.stop()
        self.root.destroy()

    def run(self):
        """运行程序"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.log_message("程序已启动，按 ~ 键开始/停止序列执行")
        self.root.mainloop()


def main():
    """主函数"""
    try:
        app = SequenceSimulator()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行出错: {e}")


if __name__ == "__main__":
    main()
