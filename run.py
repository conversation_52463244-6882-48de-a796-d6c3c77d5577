# -*- coding: utf-8 -*-
"""
简单的启动脚本
"""

import subprocess
import sys
import os

def check_and_install_dependencies():
    """检查并安装依赖"""
    try:
        import pynput
        print("✓ pynput 库已安装")
        return True
    except ImportError:
        print("正在安装 pynput 库...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pynput"])
            print("✓ pynput 库安装成功")
            return True
        except subprocess.CalledProcessError:
            print("✗ 无法安装 pynput 库")
            return False

def main():
    print("序列模拟器启动器")
    print("=" * 30)
    
    # 检查依赖
    if not check_and_install_dependencies():
        input("按回车键退出...")
        return
    
    # 启动主程序
    try:
        print("启动序列模拟器...")
        script_path = os.path.join(os.path.dirname(__file__), "sequence_simulator.py")
        subprocess.run([sys.executable, script_path])
    except Exception as e:
        print(f"启动失败: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
