序列模拟器 - 快速开始指南
============================

🚀 启动方法（任选一种）：

方法1: 直接运行Python文件
   python sequence_simulator.py

方法2: 使用Python启动器
   python run.py

方法3: 使用批处理文件（Windows）
   双击 start.bat

📝 基本使用：

1. 在"序列格式"输入框中输入按键序列
   例如：[a,1]-[b,2]-[c,1]
   
2. 选择循环模式：
   - 执行1次：只运行一遍
   - 执行多次：指定运行次数
   - 无限循环：持续运行直到停止

3. 点击"开始"按钮或按键盘上的 ~ 键开始执行

4. 按 ~ 键或点击"停止"按钮停止执行

⌨️ 序列格式说明：

格式：[按键,延迟秒数]-[按键,延迟秒数]...

常用按键：
- 普通字符：a, b, c, 1, 2, 3
- 特殊按键：space, enter, tab, ctrl, alt, shift
- 方向键：up, down, left, right
- 功能键：esc, backspace, delete, home, end

📋 示例：

输入文本：
[h,0.5]-[e,0.5]-[l,0.5]-[l,0.5]-[o,1]

带空格的文本：
[h,0.3]-[e,0.3]-[l,0.3]-[l,0.3]-[o,0.5]-[space,0.5]-[w,0.3]-[o,0.3]-[r,0.3]-[l,0.3]-[d,1]

使用特殊键：
[ctrl,0.1]-[a,0.5]-[ctrl,0.1]-[c,0.5]-[ctrl,0.1]-[v,1]

⚠️ 注意事项：

1. 确保目标程序窗口处于活动状态
2. 某些系统可能需要管理员权限
3. 延迟时间可以使用小数，如0.5秒
4. 按键会发送到当前焦点窗口

🔧 故障排除：

如果程序无法启动：
1. 确保已安装Python 3.6+
2. 安装pynput库：pip install pynput
3. 在某些系统上可能需要管理员权限

如果按键无效果：
1. 确保目标程序处于活动状态
2. 检查按键名称是否正确
3. 尝试增加延迟时间

📞 更多帮助：

查看 README.md 获取详细说明
查看 examples.md 获取更多使用示例
