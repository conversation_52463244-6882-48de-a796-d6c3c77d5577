#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试序列模拟器的基本功能
"""

import re
import sys

def test_sequence_parser():
    """测试序列解析功能"""
    print("测试序列解析功能...")
    
    # 测试用例
    test_cases = [
        ("[a,1]-[b,2]-[c,1]", True),
        ("[space,0.5]-[enter,1]", True),
        ("[ctrl,1]-[c,0.5]", True),
        ("invalid format", False),
        ("[a,1", False),
        ("[a,invalid]", False),
    ]
    
    pattern = r'\[([^,]+),(\d+(?:\.\d+)?)\]'
    
    for sequence_str, should_pass in test_cases:
        matches = re.findall(pattern, sequence_str)
        
        if should_pass:
            if matches:
                print(f"✓ '{sequence_str}' -> {matches}")
            else:
                print(f"✗ '{sequence_str}' 应该解析成功但失败了")
        else:
            if not matches:
                print(f"✓ '{sequence_str}' 正确识别为无效格式")
            else:
                print(f"✗ '{sequence_str}' 应该解析失败但成功了")

def test_imports():
    """测试必要的库导入"""
    print("\n测试库导入...")
    
    try:
        import tkinter as tk
        print("✓ tkinter 导入成功")
    except ImportError as e:
        print(f"✗ tkinter 导入失败: {e}")
        
    try:
        from pynput import keyboard
        print("✓ pynput 导入成功")
    except ImportError as e:
        print(f"✗ pynput 导入失败: {e}")
        print("请运行: pip install pynput")

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    
    # 测试序列解析
    test_sequence_parser()
    
    # 测试导入
    test_imports()

if __name__ == "__main__":
    print("序列模拟器测试程序")
    print("=" * 40)
    test_basic_functionality()
    print("\n测试完成！")
    
    # 如果pynput可用，询问是否运行主程序
    try:
        from pynput import keyboard
        print("\n所有依赖都已就绪！")
        print("您可以运行主程序: python sequence_simulator.py")
    except ImportError:
        print("\n请先安装pynput库: pip install pynput")
