# 序列模拟器

一个Python编写的键盘按键序列模拟程序，支持自定义按键序列和循环执行。

## 功能特点

- 🎯 **序列模拟**: 支持自定义按键序列，格式如 `[a,1]-[b,2]-[c,1]`
- ⌨️ **快捷键控制**: 使用 `~` 键快速开始/停止程序
- 🔄 **循环模式**: 支持执行1次、多次或无限循环
- 🖥️ **图形界面**: 直观的GUI界面，易于操作
- 📝 **实时日志**: 显示执行过程和状态信息
- 🎮 **特殊按键**: 支持空格、回车、方向键等特殊按键

## 安装要求

- Python 3.6+
- pynput库

## 安装步骤

1. 克隆或下载项目文件
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序：
   ```bash
   python sequence_simulator.py
   ```

2. 在"序列格式"输入框中输入按键序列，例如：
   - `[a,1]-[b,2]-[c,1]` - 按a键，等1秒，按b键，等2秒，按c键，等1秒
   - `[space,0.5]-[enter,1]` - 按空格键，等0.5秒，按回车键，等1秒

3. 选择循环模式：
   - **执行1次**: 序列只执行一遍
   - **执行多次**: 指定执行次数
   - **无限循环**: 持续执行直到手动停止

4. 点击"开始"按钮或按 `~` 键开始执行

5. 按 `~` 键或点击"停止"按钮停止执行

## 序列格式说明

序列格式：`[按键,延迟秒数]-[按键,延迟秒数]...`

### 支持的按键类型

**普通字符**: a, b, c, 1, 2, 3 等

**特殊按键**:
- `space` - 空格键
- `enter` - 回车键
- `tab` - Tab键
- `shift` - Shift键
- `ctrl` - Ctrl键
- `alt` - Alt键
- `esc` - Esc键
- `backspace` - 退格键
- `delete` - Delete键
- `up`, `down`, `left`, `right` - 方向键
- `home`, `end` - Home/End键
- `page_up`, `page_down` - Page Up/Down键

### 示例

```
[h,0.5]-[e,0.5]-[l,0.5]-[l,0.5]-[o,1]-[space,0.5]-[w,0.5]-[o,0.5]-[r,0.5]-[l,0.5]-[d,1]
```
这个序列会逐个输入"hello world"，每个字符间隔0.5秒。

## 注意事项

1. **权限要求**: 程序需要键盘输入权限，某些系统可能需要管理员权限
2. **焦点窗口**: 按键会发送到当前焦点窗口，请确保目标应用程序处于活动状态
3. **安全提醒**: 请谨慎使用，避免在重要应用中误操作
4. **延迟精度**: 延迟时间支持小数，如0.5秒

## 故障排除

### 程序无法启动
- 检查Python版本是否为3.6+
- 确保已安装pynput库：`pip install pynput`

### 按键无效果
- 确保目标应用程序处于活动状态
- 检查是否需要管理员权限
- 验证按键名称是否正确

### 无法监听~键
- 某些系统可能需要额外权限
- 尝试以管理员身份运行程序

## 许可证

本项目采用MIT许可证。
