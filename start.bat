@echo off
chcp 65001 >nul
echo 序列模拟器启动脚本
echo ==================

echo 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6+
    pause
    exit /b 1
)

echo 检查依赖库...
python -c "import pynput" >nul 2>&1
if errorlevel 1 (
    echo 正在安装pynput库...
    pip install pynput
    if errorlevel 1 (
        echo 错误: 无法安装pynput库
        pause
        exit /b 1
    )
)

echo 启动序列模拟器...
python sequence_simulator.py

if errorlevel 1 (
    echo 程序运行出错
    pause
)
