# 序列模拟器使用示例

## 基本示例

### 1. 简单字符输入
```
[h,0.5]-[e,0.5]-[l,0.5]-[l,0.5]-[o,1]
```
效果：逐个输入"hello"，每个字符间隔0.5秒，最后等待1秒

### 2. 带空格的文本
```
[h,0.3]-[e,0.3]-[l,0.3]-[l,0.3]-[o,0.5]-[space,0.5]-[w,0.3]-[o,0.3]-[r,0.3]-[l,0.3]-[d,1]
```
效果：输入"hello world"

### 3. 数字输入
```
[1,0.5]-[2,0.5]-[3,0.5]-[4,0.5]-[5,1]
```
效果：输入"12345"

## 特殊按键示例

### 4. 使用回车键
```
[h,0.5]-[i,1]-[enter,2]-[b,0.5]-[y,0.5]-[e,1]
```
效果：输入"hi"，按回车，等2秒，输入"bye"

### 5. 使用Tab键
```
[a,0.5]-[tab,1]-[b,0.5]-[tab,1]-[c,1]
```
效果：输入a，按Tab，输入b，按Tab，输入c

### 6. 方向键操作
```
[up,0.5]-[up,0.5]-[down,0.5]-[left,0.5]-[right,1]
```
效果：上上下左右方向键操作

### 7. 复制粘贴操作
```
[ctrl,0.1]-[a,0.5]-[ctrl,0.1]-[c,0.5]-[ctrl,0.1]-[v,1]
```
效果：全选(Ctrl+A)，复制(Ctrl+C)，粘贴(Ctrl+V)

## 实用场景示例

### 8. 游戏按键序列
```
[w,2]-[a,1]-[s,2]-[d,1]-[space,0.5]
```
效果：WASD移动序列 + 跳跃

### 9. 表单填写
```
[j,0.3]-[o,0.3]-[h,0.3]-[n,0.5]-[tab,1]-[d,0.3]-[o,0.3]-[e,0.5]-[tab,1]-[1,0.3]-[2,0.3]-[3,0.3]-[4,0.3]-[5,0.3]-[6,0.3]-[7,0.3]-[8,0.3]-[9,0.3]-[0,1]
```
效果：填写姓名"john"，Tab到下一字段，填写"doe"，Tab到下一字段，填写"1234567890"

### 10. 快捷键序列
```
[ctrl,0.1]-[shift,0.1]-[n,0.5]-[alt,0.1]-[f4,1]
```
效果：Ctrl+Shift+N，然后Alt+F4

### 11. 文本编辑
```
[ctrl,0.1]-[home,0.5]-[shift,0.1]-[end,0.5]-[delete,0.5]-[h,0.3]-[e,0.3]-[l,0.3]-[l,0.3]-[o,1]
```
效果：到行首，选择到行尾，删除，输入"hello"

### 12. 重复操作
```
[enter,1]-[up,0.5]-[enter,1]-[up,0.5]-[enter,1]
```
效果：回车，上箭头，重复3次（适合在列表中重复操作）

## 循环模式示例

### 执行1次
适合一次性操作，如登录、填表等

### 执行多次（如5次）
适合需要重复固定次数的操作，如：
- 游戏中的重复动作
- 测试中的重复输入
- 批量操作

### 无限循环
适合需要持续执行的操作，如：
- 游戏中的自动操作
- 监控程序的定期输入
- 演示程序

## 注意事项

1. **延迟时间**：根据目标程序的响应速度调整延迟时间
2. **焦点窗口**：确保目标程序处于活动状态
3. **特殊字符**：某些特殊字符可能需要使用对应的按键名称
4. **组合键**：复杂的组合键操作可能需要分解为多个步骤

## 调试技巧

1. 先用较长的延迟时间测试
2. 使用记事本等简单程序进行测试
3. 逐步减少延迟时间优化速度
4. 观察执行日志确认每步操作
